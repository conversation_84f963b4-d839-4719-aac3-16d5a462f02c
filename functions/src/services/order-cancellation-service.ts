import * as admin from "firebase-admin";
import { OrderEntity, OrderStatus, UserRole } from "../types";
import {
  spendLockedFunds,
  updateUserBalance,
  unlockFunds,
} from "./balance-service";
import {
  applyFeeToMarketplaceRevenue,
  applyFixedCancelOrderFee,
} from "./fee-service";
import { BPS_DIVISOR } from "../constants";
import { safeMultiply, safeDivide, safeSubtract } from "../utils";
import { log } from "../utils/logger";

export async function processOrderCancellation(
  order: OrderEntity,
  cancellingUserId: string
) {
  const db = admin.firestore();

  // Check if the cancelling user is an admin
  const cancellingUserDoc = await db
    .collection("users")
    .doc(cancellingUserId)
    .get();
  const cancellingUser = cancellingUserDoc.exists
    ? cancellingUserDoc.data()
    : null;
  const isAdminCancelling = cancellingUser?.role === "admin";

  const hasBothParticipants = Boolean(order.buyerId && order.sellerId);
  const isActiveSinglePersonOrder =
    order.status === OrderStatus.ACTIVE && !hasBothParticipants;
  const isPaidTwoPersonOrder =
    order.status === OrderStatus.PAID && hasBothParticipants;

  if (isActiveSinglePersonOrder) {
    return await processSinglePersonCancellation({
      order,
      cancellingUserId,
      db,
      isAdminCancelling,
    });
  } else if (isPaidTwoPersonOrder) {
    return await processTwoPersonCancellation({
      order,
      cancellingUserId,
      db,
      isAdminCancelling,
    });
  } else {
    throw new Error(
      `Order ${order.id} is not in a valid state for cancellation`
    );
  }
}

async function processSinglePersonCancellation(params: {
  order: OrderEntity;
  cancellingUserId: string;
  db: admin.firestore.Firestore;
  isAdminCancelling: boolean;
}) {
  const { order, cancellingUserId, db, isAdminCancelling } = params;
  // Use fees from order object instead of app_config
  const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage ?? 0;
  const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
  const buyerLockPercentage = buyerLockPercentageBPS / 10000; // Convert BPS to decimal
  const sellerLockPercentage = sellerLockPercentageBPS / 10000; // Convert BPS to decimal

  // Calculate locked amounts to unlock
  const buyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

  if (!isAdminCancelling) {
    // Update order status to cancelled
    await db.collection("orders").doc(order.id).update({
      status: OrderStatus.CANCELLED,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message:
        "Order cancelled by admin. All locked collateral has been released without penalties.",
      feeApplied: 0,
      feeType: "none",
    };
  }

  // Unlock funds for the cancelling user
  if (order.buyerId === cancellingUserId) {
    await unlockFunds(order.buyerId, buyerLockedAmount);
  } else if (order.sellerId === cancellingUserId) {
    await unlockFunds(order.sellerId, sellerLockedAmount);
  }

  // Apply fixed cancellation fee only if not admin
  const feeApplied = isAdminCancelling
    ? 0
    : await applyFixedCancelOrderFee(cancellingUserId);

  // Update order status to cancelled
  await db.collection("orders").doc(order.id).update({
    status: OrderStatus.CANCELLED,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  const feeMessage =
    feeApplied > 0
      ? ` A cancellation fee of ${feeApplied} TON was applied.`
      : "";

  const adminMessage = isAdminCancelling
    ? " (Admin cancellation - no fees applied)"
    : "";

  return {
    success: true,
    message: `Order cancelled successfully. Locked funds have been released.${feeMessage}${adminMessage}`,
    feeApplied,
    feeType: "fixed",
  };
}

async function processTwoPersonCancellation(params: {
  order: OrderEntity;
  cancellingUserId: string;
  db: admin.firestore.Firestore;
  isAdminCancelling: boolean;
}) {
  const { order, cancellingUserId, db, isAdminCancelling } = params;
  // Use fees from order object instead of app_config
  const cancelFeePercentageBPS = order.fees?.order_cancellation_fee ?? 0;
  const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
  const cancelFeePercentage = cancelFeePercentageBPS / 10000; // Convert BPS to decimal
  const sellerLockPercentage = sellerLockPercentageBPS / 10000; // Convert BPS to decimal

  // Calculate amounts
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

  // Check if buyer or seller is admin to skip fund unlocking
  const adminUserIds: string[] = [];
  if (order.buyerId || order.sellerId) {
    const userIds = [order.buyerId, order.sellerId].filter(Boolean) as string[];
    const userDocs = await Promise.all(
      userIds.map((id) => db.collection("users").doc(id).get())
    );

    userDocs.forEach((doc, index) => {
      if (doc.exists && doc.data()?.role === "admin") {
        adminUserIds.push(userIds[index]);
      }
    });
  }

  // If admin is cancelling, no fees are applied - just unlock collateral
  if (isAdminCancelling) {
    // Unlock collateral for both parties without any penalties
    // Skip unlocking funds for the admin if they are either buyer or seller
    // Also skip unlocking funds if buyer or seller is admin
    if (
      order.buyerId &&
      order.buyerId !== cancellingUserId &&
      !adminUserIds.includes(order.buyerId)
    ) {
      await unlockFunds(order.buyerId, order.price);
    }
    if (
      order.sellerId &&
      order.sellerId !== cancellingUserId &&
      !adminUserIds.includes(order.sellerId)
    ) {
      await unlockFunds(order.sellerId, sellerLockedAmount);
    }

    // Update order status to cancelled
    await db.collection("orders").doc(order.id).update({
      status: OrderStatus.CANCELLED,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message:
        "Order cancelled by admin. All locked collateral has been released without penalties.",
      feeApplied: 0,
      feeType: "none",
    };
  }

  // Normal cancellation logic for non-admin users
  const marketplaceFee = safeDivide(
    safeMultiply(order.price, cancelFeePercentage),
    BPS_DIVISOR
  );
  const compensationAmount = safeSubtract(order.price, marketplaceFee);

  // Determine who is cancelling and apply appropriate logic
  if (cancellingUserId === order.sellerId) {
    // Seller cancels: seller loses locked amount, buyer gets compensation
    await spendLockedFunds(order.sellerId, sellerLockedAmount);
    await updateUserBalance({
      userId: order.buyerId!,
      sumChange: compensationAmount,
      lockedChange: 0,
    });
  } else if (cancellingUserId === order.buyerId) {
    // Buyer cancels: buyer loses their payment, seller gets compensation
    await spendLockedFunds(order.buyerId, order.price);
    await updateUserBalance({
      userId: order.sellerId!,
      sumChange: compensationAmount,
      lockedChange: 0,
    });
    // Also unlock seller's locked amount
    await unlockFunds(order.sellerId!, sellerLockedAmount);
  }

  // Apply marketplace fee
  if (marketplaceFee > 0) {
    await applyFeeToMarketplaceRevenue({
      feeAmount: marketplaceFee,
      feeType: "cancel_order_penalty",
    });
  }

  // Handle reseller earnings for seller
  const resellerEarnings = order.reseller_earnings_for_seller ?? 0;
  if (resellerEarnings > 0) {
    if (cancellingUserId === order.sellerId) {
      // Seller cancels: reseller earnings go to marketplace revenue
      await applyFeeToMarketplaceRevenue({
        feeAmount: resellerEarnings,
        feeType: "reseller_earnings_seller_cancel",
      });
      log.feeLog(
        "Reseller earnings added to marketplace revenue (seller cancelled)",
        {
          feeAmount: resellerEarnings,
          feeType: "reseller_earnings_seller_cancel",
          orderId: order.id,
        }
      );
    } else if (cancellingUserId === order.buyerId) {
      // Buyer cancels: reseller earnings go to seller
      await updateUserBalance({
        userId: order.sellerId!,
        sumChange: resellerEarnings,
        lockedChange: 0,
      });
      log.balanceLog("Reseller earnings added to seller (buyer cancelled)", {
        userId: order.sellerId!,
        amount: resellerEarnings,
        operation: "reseller_earnings_buyer_cancel",
        orderId: order.id,
      });
    }
  }

  // Delete the order (same as expired orders)
  await db.collection("orders").doc(order.id).delete();

  const cancellerRole =
    cancellingUserId === order.sellerId ? UserRole.SELLER : UserRole.BUYER;
  const compensatedRole =
    cancellingUserId === order.sellerId ? UserRole.BUYER : UserRole.SELLER;

  return {
    success: true,
    message: `Order cancelled by ${cancellerRole}. ${compensatedRole} received ${compensationAmount} TON compensation. Marketplace fee: ${marketplaceFee} TON.`,
    feeApplied: marketplaceFee,
    feeType: "dynamic",
  };
}

export async function validateCancellationPermission(
  order: OrderEntity,
  userId: string
) {
  // Check if the user is an admin - admins can cancel any order
  const db = admin.firestore();
  const userDoc = await db.collection("users").doc(userId).get();
  const userData = userDoc.exists ? userDoc.data() : null;
  const isAdmin = userData?.role === "admin";

  // Skip permission validation for admin users
  if (!isAdmin) {
    if (order.buyerId !== userId && order.sellerId !== userId) {
      throw new Error(
        "You can only cancel orders where you are the buyer or seller."
      );
    }
  }

  if (order.status === OrderStatus.FULFILLED) {
    throw new Error("Cannot cancel a fulfilled order.");
  }

  if (order.status === OrderStatus.CANCELLED) {
    throw new Error("Order is already cancelled.");
  }

  if (order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    throw new Error(
      "Cannot cancel an order where the gift has already been sent to relayer."
    );
  }
}
